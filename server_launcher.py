"""
游戏服务器启动器 - 基于Tkinter的GUI应用
用于管理和启动游戏服务器
"""
import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import socket
import http.server
import socketserver
import webbrowser
import logging
from datetime import datetime
import json
import importlib.util

# 处理打包后的路径
def get_application_path():
    """获取应用程序路径，兼容打包和非打包环境"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的应用程序
        return os.path.dirname(sys.executable)
    else:
        # 如果是开发环境
        return os.path.dirname(os.path.abspath(__file__))

# 添加utils目录到系统路径
utils_path = os.path.join(get_application_path(), 'utils')
if os.path.exists(utils_path) and utils_path not in sys.path:
    sys.path.append(utils_path)

# 动态导入network_utils模块
try:
    # 首先尝试直接导入
    from utils.network_utils import get_local_ip, find_available_port
    logging.debug("成功通过标准导入加载network_utils模块")
except ImportError:
    # 如果直接导入失败，尝试动态导入
    try:
        # 尝试从utils目录加载
        network_utils_path = os.path.join(utils_path, 'network_utils.py')
        logging.debug(f"尝试从路径加载network_utils: {network_utils_path}")
        
        if os.path.exists(network_utils_path):
            spec = importlib.util.spec_from_file_location("network_utils", network_utils_path)
            network_utils = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(network_utils)
            get_local_ip = network_utils.get_local_ip
            find_available_port = network_utils.find_available_port
            logging.debug("成功通过动态导入加载network_utils模块")
        elif hasattr(sys, '_MEIPASS'):
            # 如果在PyInstaller环境中，尝试从_MEIPASS中加载
            meipass_utils_path = os.path.join(sys._MEIPASS, 'utils', 'network_utils.py')
            logging.debug(f"尝试从PyInstaller临时目录加载network_utils: {meipass_utils_path}")
            
            if os.path.exists(meipass_utils_path):
                spec = importlib.util.spec_from_file_location("network_utils", meipass_utils_path)
                network_utils = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(network_utils)
                get_local_ip = network_utils.get_local_ip
                find_available_port = network_utils.find_available_port
                logging.debug("成功从PyInstaller临时目录加载network_utils模块")
            else:
                # 如果在打包环境中也找不到，使用默认实现
                logging.warning("无法找到network_utils模块，使用内置实现")
                def get_local_ip():
                    try:
                        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                        s.connect(("*******", 80))
                        ip = s.getsockname()[0]
                        s.close()
                        return ip
                    except Exception:
                        return "127.0.0.1"

                def find_available_port(start_port=8006):
                    port = start_port
                    while True:
                        try:
                            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                            s.bind(('', port))
                            s.close()
                            return port
                        except OSError:
                            port += 1
                        except Exception:
                            return start_port + 1000
        else:
            # 如果找不到模块，提供默认实现
            logging.warning("无法找到network_utils模块，使用内置实现")
            def get_local_ip():
                try:
                    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    s.connect(("*******", 80))
                    ip = s.getsockname()[0]
                    s.close()
                    return ip
                except Exception:
                    return "127.0.0.1"

            def find_available_port(start_port=8006):
                port = start_port
                while True:
                    try:
                        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        s.bind(('', port))
                        s.close()
                        return port
                    except OSError:
                        port += 1
                    except Exception:
                        return start_port + 1000
    except Exception as e:
        logging.error(f"导入network_utils模块失败: {e}")
        # 提供默认实现
        def get_local_ip():
            return "127.0.0.1"

        def find_available_port(start_port=8006):
            return start_port

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 改为DEBUG级别以获取更多信息
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 全局变量
ADMIN_PASSWORD = "12340"  # 注意：这是一个简化处理，实际应用中应使用更安全的方式存储密码

# 获取游戏目录路径
def get_games_dir():
    """获取游戏目录路径，兼容打包和非打包环境"""
    app_path = get_application_path()
    games_dir = os.path.join(app_path, "games")

    # 记录尝试的路径
    logging.info(f"尝试查找游戏目录: {games_dir}")

    # 记录所有可能路径，方便调试
    possible_paths = [games_dir]

    # 如果games目录不存在，尝试在上级目录查找
    if not os.path.exists(games_dir):
        parent_dir = os.path.dirname(app_path)
        games_dir = os.path.join(parent_dir, "games")
        logging.info(f"尝试在上级目录查找游戏目录: {games_dir}")
        possible_paths.append(games_dir)

    # 如果在打包环境中，尝试更多可能的位置
    if getattr(sys, 'frozen', False) and not os.path.exists(games_dir):
        # 尝试从_MEIPASS中查找（PyInstaller的临时目录）
        if hasattr(sys, '_MEIPASS'):
            meipass_dir = os.path.join(sys._MEIPASS, "games")
            logging.info(f"尝试在PyInstaller临时目录中查找游戏目录: {meipass_dir}")
            possible_paths.append(meipass_dir)
            if os.path.exists(meipass_dir):
                games_dir = meipass_dir

        # 尝试在release目录下查找
        release_dir = os.path.join(app_path, "release", "games")
        logging.info(f"尝试在release目录下查找游戏目录: {release_dir}")
        possible_paths.append(release_dir)
        if os.path.exists(release_dir):
            games_dir = release_dir

    # 如果仍然找不到，尝试在当前工作目录中查找
    if not os.path.exists(games_dir):
        cwd_dir = os.path.join(os.getcwd(), "games")
        logging.info(f"尝试在当前工作目录中查找游戏目录: {cwd_dir}")
        possible_paths.append(cwd_dir)
        if os.path.exists(cwd_dir):
            games_dir = cwd_dir

    # 记录所有尝试过的路径
    logging.debug(f"尝试过的所有游戏目录路径: {possible_paths}")

    # 如果找不到任何目录，报告错误
    if not os.path.exists(games_dir):
        logging.error(f"无法找到游戏目录，所有尝试都失败: {possible_paths}")
    else:
        # 记录目录内容
        try:
            logging.debug(f"找到游戏目录: {games_dir}")
            logging.debug(f"游戏目录内容: {os.listdir(games_dir)}")
        except Exception as e:
            logging.error(f"无法列出游戏目录内容: {e}")

    # 记录最终使用的游戏目录
    logging.info(f"最终使用的游戏目录: {games_dir}")
    return games_dir

GAMES_DIR = get_games_dir()
running_servers = {}  # 存储运行中的服务器信息 {game_name: {"server": server_obj, "port": port, "thread": thread}}

# 游戏配置
game_configs = {
    "gomoku": {
        "difficulty": 1  # 默认难度：1=简单，2=中等，3=困难
    }
}

class ThreadingHTTPServer(socketserver.ThreadingMixIn, http.server.HTTPServer):
    """支持多线程的HTTP服务器"""
    pass

class GameRequestHandler(http.server.SimpleHTTPRequestHandler):
    """处理游戏服务请求的处理器"""

    def __init__(self, *args, game_dir=None, game_name=None, **kwargs):
        self.game_dir = game_dir
        self.game_name = game_name
        
        # 记录游戏目录信息，便于调试
        logging.info(f"初始化GameRequestHandler: 游戏={game_name}, 目录={game_dir}")
        
        # 检查游戏目录是否存在
        if not os.path.exists(game_dir):
            logging.warning(f"游戏目录不存在: {game_dir}")
            
            # 尝试在打包环境中查找游戏目录 - 更全面的查找策略
            if getattr(sys, 'frozen', False):
                # 尝试在可执行文件目录下查找
                alt_dir = os.path.join(get_application_path(), "games", game_name)
                logging.debug(f"尝试查找游戏目录1: {alt_dir}")
                if os.path.exists(alt_dir):
                    logging.info(f"在打包环境中找到游戏目录: {alt_dir}")
                    self.game_dir = alt_dir
                elif hasattr(sys, '_MEIPASS'):
                    # 尝试在PyInstaller临时目录中查找
                    alt_dir = os.path.join(sys._MEIPASS, "games", game_name)
                    logging.debug(f"尝试查找游戏目录2: {alt_dir}")
                    if os.path.exists(alt_dir):
                        logging.info(f"在PyInstaller临时目录中找到游戏目录: {alt_dir}")
                        self.game_dir = alt_dir
                
                # 尝试在release目录下查找
                release_dir = os.path.join(get_application_path(), "release", "games", game_name)
                logging.debug(f"尝试查找游戏目录3: {release_dir}")
                if os.path.exists(release_dir):
                    logging.info(f"在release目录中找到游戏目录: {release_dir}")
                    self.game_dir = release_dir
                
                # 尝试在上级目录查找
                parent_dir = os.path.dirname(get_application_path())
                parent_game_dir = os.path.join(parent_dir, "games", game_name)
                logging.debug(f"尝试查找游戏目录4: {parent_game_dir}")
                if os.path.exists(parent_game_dir):
                    logging.info(f"在上级目录中找到游戏目录: {parent_game_dir}")
                    self.game_dir = parent_game_dir
            
            # 最后检查目录是否存在
            if not os.path.exists(self.game_dir):
                logging.error(f"找不到游戏目录，所有尝试都失败: {self.game_dir}")
        
        # 检查目录中的文件
        if os.path.exists(self.game_dir):
            logging.debug(f"游戏目录内容: {os.listdir(self.game_dir)}")

        super().__init__(*args, **kwargs)

    def log_message(self, format, *args):
        """重写日志方法，使用我们自己的日志系统"""
        logging.debug(f"{self.address_string()} - {format % args}")

    def translate_path(self, path):
        """重写路径转换，将请求映射到游戏目录"""
        # 记录原始请求路径
        logging.debug(f"原始请求路径: {path}")
        
        # 处理心跳请求和API请求
        if path == '/heartbeat' or path.startswith('/api/'):
            return path
        
        # 从URL路径中获取相对路径，删除开头的/
        if path.startswith('/'):
            path = path[1:]
        
        # 如果是空路径或只有'/'，则返回index.html
        if path == '' or path == '/':
            path = 'index.html'
        
        # 构建游戏目录中的完整路径
        full_path = os.path.join(self.game_dir, path)
        logging.debug(f"请求路径映射: {path} -> {full_path}")
        
        # 检查文件是否存在
        if os.path.exists(full_path) and not os.path.isdir(full_path):
            logging.debug(f"找到文件: {full_path}")
            return full_path
        
        # 如果找不到文件，尝试解析子目录
        logging.warning(f"文件不存在: {full_path}")
        
        # 特殊处理，对于一些常见的静态资源路径模式
        for pattern in ['css/', 'js/', 'images/', 'img/']:
            if pattern in path:
                # 尝试在根目录查找
                alt_path = os.path.join(self.game_dir, os.path.basename(path))
                logging.debug(f"尝试替代路径: {alt_path}")
                if os.path.exists(alt_path):
                    logging.info(f"找到替代文件: {alt_path}")
                    return alt_path
        
        return full_path

    def do_GET(self):
        """处理GET请求"""
        if self.path == '/heartbeat':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'status': 'ok', 'timestamp': datetime.now().isoformat()}
            self.wfile.write(json.dumps(response).encode())
            return
        elif self.path == '/api/config':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()

            # 获取游戏配置（仅难度）
            if self.game_name in game_configs:
                config = game_configs[self.game_name]
            else:
                config = {"difficulty": 1}

            self.wfile.write(json.dumps(config).encode())
            return
        
        # 处理静态资源文件，确保设置正确的内容类型
        path = self.translate_path(self.path)
        if os.path.exists(path) and not os.path.isdir(path):
            try:
                # 确定文件的MIME类型
                content_type = self.guess_type(path)
                f = open(path, 'rb')
                fs = os.fstat(f.fileno())
                
                self.send_response(200)
                self.send_header("Content-type", content_type)
                self.send_header("Content-Length", str(fs[6]))
                self.send_header("Last-Modified", self.date_time_string(fs.st_mtime))
                self.end_headers()
                
                try:
                    # 发送文件内容
                    self.copyfile(f, self.wfile)
                finally:
                    f.close()
                return
            except Exception as e:
                logging.error(f"处理静态文件时出错: {e}")
        
        # 如果不是特殊处理的文件，使用默认处理方式
        return super().do_GET()
        
    def guess_type(self, path):
        """根据文件扩展名猜测MIME类型"""
        base, ext = os.path.splitext(path)
        if ext in {'.html', '.htm'}:
            return 'text/html'
        elif ext == '.css':
            return 'text/css'
        elif ext == '.js':
            return 'application/javascript'
        elif ext in {'.jpg', '.jpeg'}:
            return 'image/jpeg'
        elif ext == '.png':
            return 'image/png'
        elif ext == '.gif':
            return 'image/gif'
        elif ext == '.svg':
            return 'image/svg+xml'
        elif ext == '.json':
            return 'application/json'
        else:
            return 'application/octet-stream'
            
    def copyfile(self, source, outputfile):
        """将文件内容复制到输出流"""
        buf_size = 64 * 1024  # 64KB buffer
        buf = source.read(buf_size)
        while buf:
            outputfile.write(buf)
            buf = source.read(buf_size)

def create_request_handler(game_dir, game_name):
    """创建请求处理器类的工厂函数"""
    return lambda *args, **kwargs: GameRequestHandler(*args, game_dir=game_dir, game_name=game_name, **kwargs)

def start_game_server(game_name, port, log_callback, difficulty=None):
    """
    启动游戏服务器

    参数:
        game_name (str): 游戏名称
        port (int): 端口号
        log_callback (callable): 日志回调函数
        difficulty (int, optional): 游戏难度
    """
    game_dir = os.path.join(GAMES_DIR, game_name)
    if not os.path.exists(game_dir):
        log_callback(f"错误: 游戏目录 '{game_dir}' 不存在")
        return None

    # 确保游戏配置存在
    if game_name not in game_configs:
        game_configs[game_name] = {
            "difficulty": 1,
            "player_score": 0,
            "computer_score": 0
        }

    # 如果提供了难度，则更新配置
    if difficulty is not None:
        game_configs[game_name]["difficulty"] = difficulty
        log_callback(f"设置 {game_name} 难度为 {difficulty}")

    try:
        handler = create_request_handler(game_dir, game_name)
        server = ThreadingHTTPServer(("", port), handler)
        log_callback(f"启动 {game_name} 服务器在端口 {port}")

        # 在新线程中启动服务器
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True  # 设置为守护线程，这样主程序退出时线程会自动结束
        server_thread.start()

        # 记录服务器信息
        running_servers[game_name] = {
            "server": server,
            "port": port,
            "thread": server_thread,
            "status": "运行中",
            "difficulty": game_configs[game_name]["difficulty"]
        }

        return server
    except Exception as e:
        log_callback(f"启动服务器时出错: {e}")
        return None

def stop_game_server(game_name, log_callback):
    """
    停止游戏服务器

    参数:
        game_name (str): 游戏名称
        log_callback (callable): 日志回调函数
    """
    if game_name in running_servers:
        try:
            server_info = running_servers[game_name]
            server_info["server"].shutdown()
            server_info["server"].server_close()
            log_callback(f"停止 {game_name} 服务器")
            server_info["status"] = "已停止"
            return True
        except Exception as e:
            log_callback(f"停止服务器时出错: {e}")
            return False
    else:
        log_callback(f"错误: 服务器 '{game_name}' 未运行")
        return False

def stop_all_servers(log_callback):
    """停止所有运行中的服务器"""
    for game_name in list(running_servers.keys()):
        stop_game_server(game_name, log_callback)

class PasswordDialog(tk.Toplevel):
    """密码验证对话框"""

    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.result = False

        self.title("验证")
        self.geometry("300x150")
        self.resizable(False, False)

        # 设置窗口在屏幕中央
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'+{x}+{y}')

        # 创建控件
        tk.Label(self, text="请输入:", font=("Arial", 12)).pack(pady=10)

        self.password_var = tk.StringVar()
        self.password_entry = tk.Entry(self, textvariable=self.password_var, show="*", font=("Arial", 12))
        self.password_entry.pack(pady=5, padx=20, fill=tk.X)

        button_frame = tk.Frame(self)
        button_frame.pack(pady=10, fill=tk.X)

        tk.Button(button_frame, text="确定", command=self.verify_password, width=10).pack(side=tk.LEFT, padx=20)
        tk.Button(button_frame, text="取消", command=self.cancel, width=10).pack(side=tk.RIGHT, padx=20)

        # 绑定回车键
        self.bind("<Return>", lambda _: self.verify_password())
        self.bind("<Escape>", lambda _: self.cancel())

        # 设置焦点
        self.password_entry.focus_set()

        # 设置为模态对话框
        self.transient(parent)
        self.grab_set()
        parent.wait_window(self)

    def verify_password(self):
        """验证密码"""
        if self.password_var.get() == ADMIN_PASSWORD:
            self.result = True
            self.destroy()
        else:
            messagebox.showerror("错误", "错误，请重试")
            self.password_var.set("")
            self.password_entry.focus_set()

    def cancel(self):
        """取消操作"""
        self.result = False
        self.destroy()

class GameServerLauncher(tk.Tk):
    """游戏服务器启动器主窗口"""

    def __init__(self):
        super().__init__()

        self.title("启动")
        self.geometry("800x600")
        self.minsize(800, 600)

        # 验证密码
        if not self.verify_admin():
            self.destroy()
            sys.exit(0)

        # 创建界面
        self.create_widgets()

        # 加载游戏列表
        self.load_games()

        # 设置关闭窗口事件
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def verify_admin(self):
        """验证管理员密码"""
        dialog = PasswordDialog(self)
        return dialog.result

    def create_widgets(self):
        """创建界面控件"""
        # 创建主框架
        main_frame = ttk.Frame(self, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧面板 - 游戏列表和控制按钮
        left_frame = ttk.LabelFrame(main_frame, text="游戏管理", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 游戏列表
        games_frame = ttk.Frame(left_frame)
        games_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        ttk.Label(games_frame, text="可用游戏:").pack(anchor=tk.W)

        # 创建带滚动条的列表框
        list_frame = ttk.Frame(games_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.games_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, font=("Arial", 11))
        self.games_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar.config(command=self.games_listbox.yview)

        # 绑定选择事件
        self.games_listbox.bind('<<ListboxSelect>>', self.on_game_selected)

        # 游戏设置框架
        settings_frame = ttk.LabelFrame(left_frame, text="游戏设置")
        settings_frame.pack(fill=tk.X, pady=5)

        # 五子棋难度设置
        self.gomoku_settings_frame = ttk.Frame(settings_frame)
        self.gomoku_settings_frame.pack(fill=tk.X, pady=5, padx=5)

        ttk.Label(self.gomoku_settings_frame, text="五子棋难度:").grid(row=0, column=0, sticky=tk.W, pady=2)

        self.difficulty_var = tk.IntVar(value=1)

        # 难度单选按钮
        difficulty_frame = ttk.Frame(self.gomoku_settings_frame)
        difficulty_frame.grid(row=0, column=1, sticky=tk.W, pady=2)

        ttk.Radiobutton(difficulty_frame, text="简单", variable=self.difficulty_var, value=1).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(difficulty_frame, text="中等", variable=self.difficulty_var, value=2).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(difficulty_frame, text="困难", variable=self.difficulty_var, value=3).pack(side=tk.LEFT, padx=5)

        # 显示游戏统计信息
        self.stats_frame = ttk.Frame(settings_frame)
        self.stats_frame.pack(fill=tk.X, pady=5, padx=5)

        ttk.Label(self.stats_frame, text="玩家胜场:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.player_score_var = tk.StringVar(value="0")
        ttk.Label(self.stats_frame, textvariable=self.player_score_var).grid(row=0, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.stats_frame, text="电脑胜场:").grid(row=0, column=2, sticky=tk.W, pady=2, padx=(20, 0))
        self.computer_score_var = tk.StringVar(value="0")
        ttk.Label(self.stats_frame, textvariable=self.computer_score_var).grid(row=0, column=3, sticky=tk.W, pady=2)

        # 控制按钮
        buttons_frame = ttk.Frame(left_frame)
        buttons_frame.pack(fill=tk.X, pady=5)

        self.start_button = ttk.Button(buttons_frame, text="启动服务", command=self.start_server)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(buttons_frame, text="停止服务", command=self.stop_server, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        self.refresh_button = ttk.Button(buttons_frame, text="刷新列表", command=self.load_games)
        self.refresh_button.pack(side=tk.LEFT, padx=5)

        # 右侧面板 - 服务信息和日志
        right_frame = ttk.LabelFrame(main_frame, text="服务信息", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 服务信息
        info_frame = ttk.Frame(right_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(info_frame, text="当前服务:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.current_game_var = tk.StringVar(value="无")
        ttk.Label(info_frame, textvariable=self.current_game_var).grid(row=0, column=1, sticky=tk.W, pady=2)

        ttk.Label(info_frame, text="状态:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.status_var = tk.StringVar(value="未启动")
        ttk.Label(info_frame, textvariable=self.status_var).grid(row=1, column=1, sticky=tk.W, pady=2)

        ttk.Label(info_frame, text="访问地址:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.url_var = tk.StringVar(value="")
        ttk.Label(info_frame, textvariable=self.url_var).grid(row=2, column=1, sticky=tk.W, pady=2)

        # 浏览器打开按钮
        self.open_browser_button = ttk.Button(info_frame, text="浏览器打开", command=self.open_in_browser, state=tk.DISABLED)
        self.open_browser_button.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 日志区域
        log_frame = ttk.LabelFrame(right_frame, text="操作日志")
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=("Consolas", 10))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.log_text.config(state=tk.DISABLED)

        # 状态栏
        status_frame = ttk.Frame(self)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)

        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)

        # 添加日志
        self.log("游戏服务器启动器已启动")

    def load_games(self):
        """加载游戏列表"""
        self.games_listbox.delete(0, tk.END)

        if not os.path.exists(GAMES_DIR):
            self.log(f"错误: 游戏目录 '{GAMES_DIR}' 不存在")
            # 尝试创建游戏目录
            try:
                os.makedirs(GAMES_DIR)
                self.log(f"已创建游戏目录: {GAMES_DIR}")
            except Exception as e:
                self.log(f"创建游戏目录失败: {e}")
            return

        games = []
        try:
            for item in os.listdir(GAMES_DIR):
                if os.path.isdir(os.path.join(GAMES_DIR, item)):
                    games.append(item)

                    # 添加到列表框
                    status = "未启动"
                    if item in running_servers:
                        status = running_servers[item]["status"]

                    self.games_listbox.insert(tk.END, f"{item} [{status}]")

            if not games:
                self.log(f"未找到游戏，请确保游戏文件夹存在于 '{GAMES_DIR}' 目录中")
            else:
                self.log(f"找到 {len(games)} 个游戏")
        except Exception as e:
            self.log(f"加载游戏列表时出错: {e}")

    def on_game_selected(self, _):
        """游戏选择事件处理"""
        if not self.games_listbox.curselection():
            return

        # 获取选中的游戏名称
        selected_item = self.games_listbox.get(self.games_listbox.curselection())
        game_name = selected_item.split(" [")[0]  # 提取游戏名称

        # 更新界面
        self.current_game_var.set(game_name)

        # 更新游戏设置界面
        if game_name == "gomoku":
            self.gomoku_settings_frame.pack(fill=tk.X, pady=5, padx=5)

            # 更新难度设置
            if game_name in game_configs:
                self.difficulty_var.set(game_configs[game_name]["difficulty"])
            else:
                self.difficulty_var.set(1)

            # 分数显示已移至客户端，这里不再显示
            self.player_score_var.set("--")
            self.computer_score_var.set("--")
        else:
            self.gomoku_settings_frame.pack_forget()

        if game_name in running_servers:
            self.status_var.set(running_servers[game_name]["status"])
            if running_servers[game_name]["status"] == "运行中":
                port = running_servers[game_name]["port"]
                ip = get_local_ip() or "127.0.0.1"
                self.url_var.set(f"http://{ip}:{port}")
                self.stop_button.config(state=tk.NORMAL)
                self.open_browser_button.config(state=tk.NORMAL)
            else:
                self.url_var.set("")
                self.stop_button.config(state=tk.DISABLED)
                self.open_browser_button.config(state=tk.DISABLED)
        else:
            self.status_var.set("未启动")
            self.url_var.set("")
            self.stop_button.config(state=tk.DISABLED)
            self.open_browser_button.config(state=tk.DISABLED)

    def start_server(self):
        """启动服务器"""
        if not self.games_listbox.curselection():
            messagebox.showwarning("警告", "请先选择一个游戏")
            return

        # 获取选中的游戏名称
        selected_item = self.games_listbox.get(self.games_listbox.curselection())
        game_name = selected_item.split(" [")[0]  # 提取游戏名称

        # 检查服务器是否已经运行
        if game_name in running_servers and running_servers[game_name]["status"] == "运行中":
            messagebox.showinfo("提示", f"{game_name} 服务器已经在运行中")
            return

        # 查找可用端口
        port = find_available_port()

        # 获取游戏设置
        difficulty = None
        if game_name == "gomoku":
            difficulty = self.difficulty_var.get()

        # 启动服务器
        server = start_game_server(game_name, port, self.log, difficulty)
        if server:
            # 更新界面
            self.status_var.set("运行中")
            ip = get_local_ip() or "127.0.0.1"
            self.url_var.set(f"http://{ip}:{port}")
            self.stop_button.config(state=tk.NORMAL)
            self.open_browser_button.config(state=tk.NORMAL)

            # 刷新游戏列表
            self.load_games()

            # 选中当前游戏
            for i in range(self.games_listbox.size()):
                if game_name in self.games_listbox.get(i):
                    self.games_listbox.selection_set(i)
                    break

    def stop_server(self):
        """停止服务器"""
        if not self.games_listbox.curselection():
            messagebox.showwarning("警告", "请先选择一个游戏")
            return

        # 获取选中的游戏名称
        selected_item = self.games_listbox.get(self.games_listbox.curselection())
        game_name = selected_item.split(" [")[0]  # 提取游戏名称

        # 停止服务器
        if stop_game_server(game_name, self.log):
            # 更新界面
            self.status_var.set("已停止")
            self.url_var.set("")
            self.stop_button.config(state=tk.DISABLED)
            self.open_browser_button.config(state=tk.DISABLED)

            # 刷新游戏列表
            self.load_games()

            # 选中当前游戏
            for i in range(self.games_listbox.size()):
                if game_name in self.games_listbox.get(i):
                    self.games_listbox.selection_set(i)
                    break

    def open_in_browser(self):
        """在浏览器中打开游戏"""
        url = self.url_var.get()
        if url:
            webbrowser.open(url)
            self.log(f"在浏览器中打开 {url}")
        else:
            messagebox.showwarning("警告", "没有可用的URL")

    def log(self, message):
        """添加日志"""
        self.log_text.config(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

        # 更新状态栏
        self.status_label.config(text=message)

    def on_closing(self):
        """窗口关闭事件处理"""
        if running_servers:
            if messagebox.askyesno("确认", "有服务器正在运行，关闭窗口将停止所有服务器。是否继续?"):
                stop_all_servers(self.log)
                self.destroy()
        else:
            self.destroy()

if __name__ == "__main__":
    app = GameServerLauncher()
    app.mainloop()
