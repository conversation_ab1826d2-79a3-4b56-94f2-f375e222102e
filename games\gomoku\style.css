/* 基本样式 */
body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    padding: 20px;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
}

h1 {
    text-align: center;
    color: #333;
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

/* 游戏信息 */
.game-info {
    margin-bottom: 20px;
    display: block;
    overflow: hidden;
    clear: both;
}

#status {
    float: left;
    font-weight: bold;
}

#connection-status {
    float: right;
    padding: 2px 8px;
    border-radius: 3px;
    font-weight: bold;
    position: absolute;
    top: 20px;
    right: 20px;
}

#connection-status.connected {
    background-color: #dff0d8;
    color: #3c763d;
}

#connection-status.disconnected {
    background-color: #f2dede;
    color: #a94442;
}

/* 游戏容器 */
.game-container {
    display: block;
    text-align: center;
    margin-bottom: 20px;
    position: relative;
}

/* 分数显示 */
.score-display {
    float: right;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 5px;
    font-weight: bold;
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 5px;
    width: fit-content;
    margin-right: 10px;
}

.score-display span {
    margin: 0;
}

/* 棋盘样式 */
.board {
    display: inline-block;
    background-color: #dcb35c;
    padding: 10px;
    border: 2px solid #8c6d3f;
    position: relative;
}

.board-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.cell {
    position: absolute;
    box-sizing: border-box;
    cursor: pointer;
}

.cell:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.stone {
    position: absolute;
    border-radius: 50%;
    box-sizing: border-box;
}

.stone.black {
    background: radial-gradient(circle at 30% 30%, #666, #000);
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.stone.white {
    background: radial-gradient(circle at 30% 30%, #fff, #ddd);
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    border: 1px solid #ccc;
}

/* 棋子动画效果 */
.stone-animation {
    animation: stone-drop 0.3s ease-out;
}

@keyframes stone-drop {
    0% {
        transform: scale(1.3) translateY(-10px);
        opacity: 0.7;
    }
    70% {
        transform: scale(1.1) translateY(2px);
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* 控制区域 */
.controls {
    text-align: center;
    margin-top: 20px;
    overflow: hidden;
}

button {
    background-color: #4CAF50;
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 4px;
}

button:hover {
    background-color: #45a049;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* 已移除原来的分数样式，现在使用 .score-display */

/* 响应式设计 */
@media (max-width: 600px) {
    .container {
        padding: 10px;
    }

    h1 {
        font-size: 24px;
    }

    .board {
        padding: 5px;
    }
}
