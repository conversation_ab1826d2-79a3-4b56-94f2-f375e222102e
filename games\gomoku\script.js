/**
 * 五子棋游戏 - 人机对战
 * 兼容IE11及现代浏览器
 */

// 游戏配置
var BOARD_SIZE = 15; // 15x15的棋盘
var CELL_SIZE = 30;  // 每个格子30像素
var STONE_SIZE = 28; // 棋子大小

// 游戏状态
var gameState = {
    board: [], // 棋盘状态: 0=空, 1=黑子, 2=白子
    currentPlayer: 1, // 1=玩家(黑子), 2=电脑(白子)
    gameOver: false,
    playerScore: 0,
    computerScore: 0,
    disconnected: false,
    difficulty: 1 // 默认难度: 1=简单, 2=中等, 3=困难
};

// 从本地存储加载分数
function loadScoresFromLocalStorage() {
    var playerScore = localStorage.getItem('gomoku_player_score');
    var computerScore = localStorage.getItem('gomoku_computer_score');

    if (playerScore !== null) {
        gameState.playerScore = parseInt(playerScore, 10);
    }

    if (computerScore !== null) {
        gameState.computerScore = parseInt(computerScore, 10);
    }
}

// 保存分数到本地存储
function saveScoresToLocalStorage() {
    localStorage.setItem('gomoku_player_score', gameState.playerScore);
    localStorage.setItem('gomoku_computer_score', gameState.computerScore);
}

// DOM元素
var boardElement;
var statusElement;
var connectionStatusElement;
var restartButton;
var playerScoreElement;
var computerScoreElement;

// 心跳检测
var heartbeatInterval;
var failedHeartbeats = 0;
var MAX_FAILED_HEARTBEATS = 3;

/**
 * 初始化游戏
 */
function initGame() {
    // 获取DOM元素
    boardElement = document.getElementById('board');
    statusElement = document.getElementById('status');
    connectionStatusElement = document.getElementById('connection-status');
    restartButton = document.getElementById('restart-btn');
    playerScoreElement = document.getElementById('player-score');
    computerScoreElement = document.getElementById('computer-score');

    // 设置棋盘大小
    boardElement.style.width = (BOARD_SIZE * CELL_SIZE) + 'px';
    boardElement.style.height = (BOARD_SIZE * CELL_SIZE) + 'px';

    // 创建棋盘网格
    createBoardGrid();

    // 从本地存储加载分数
    loadScoresFromLocalStorage();

    // 获取游戏配置（仅获取难度设置）
    fetchGameConfig();

    // 初始化棋盘状态
    resetBoard();

    // 绑定事件
    restartButton.onclick = restartGame;

    // 开始心跳检测
    startHeartbeat();

    // 更新分数显示
    updateScoreDisplay();
}

/**
 * 创建棋盘网格
 */
function createBoardGrid() {
    // 创建网格容器
    var gridElement = document.createElement('div');
    gridElement.className = 'board-grid';
    boardElement.appendChild(gridElement);

    // 创建单元格
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            var cell = document.createElement('div');
            cell.className = 'cell';
            cell.style.width = CELL_SIZE + 'px';
            cell.style.height = CELL_SIZE + 'px';
            cell.style.left = (x * CELL_SIZE) + 'px';
            cell.style.top = (y * CELL_SIZE) + 'px';

            // 存储坐标
            cell.setAttribute('data-x', x);
            cell.setAttribute('data-y', y);

            // 绑定点击事件
            cell.onclick = handleCellClick;

            gridElement.appendChild(cell);
        }
    }

    // 绘制棋盘线
    drawBoardLines();
}

/**
 * 绘制棋盘线
 */
function drawBoardLines() {
    // 创建一个canvas元素来绘制线条
    var canvas = document.createElement('canvas');
    canvas.width = BOARD_SIZE * CELL_SIZE;
    canvas.height = BOARD_SIZE * CELL_SIZE;
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.pointerEvents = 'none'; // 让点击事件穿透canvas

    boardElement.appendChild(canvas);

    var ctx = canvas.getContext('2d');
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 1;

    // 绘制横线
    for (var i = 0; i < BOARD_SIZE; i++) {
        ctx.beginPath();
        ctx.moveTo(CELL_SIZE / 2, i * CELL_SIZE + CELL_SIZE / 2);
        ctx.lineTo(BOARD_SIZE * CELL_SIZE - CELL_SIZE / 2, i * CELL_SIZE + CELL_SIZE / 2);
        ctx.stroke();
    }

    // 绘制竖线
    for (var i = 0; i < BOARD_SIZE; i++) {
        ctx.beginPath();
        ctx.moveTo(i * CELL_SIZE + CELL_SIZE / 2, CELL_SIZE / 2);
        ctx.lineTo(i * CELL_SIZE + CELL_SIZE / 2, BOARD_SIZE * CELL_SIZE - CELL_SIZE / 2);
        ctx.stroke();
    }

    // 绘制天元和星位
    var starPoints = [
        {x: 3, y: 3}, {x: 3, y: 11},
        {x: 7, y: 7}, // 天元
        {x: 11, y: 3}, {x: 11, y: 11}
    ];

    ctx.fillStyle = '#000';
    for (var i = 0; i < starPoints.length; i++) {
        var point = starPoints[i];
        ctx.beginPath();
        ctx.arc(
            point.x * CELL_SIZE + CELL_SIZE / 2,
            point.y * CELL_SIZE + CELL_SIZE / 2,
            3, 0, Math.PI * 2
        );
        ctx.fill();
    }
}

/**
 * 重置棋盘状态
 */
function resetBoard() {
    // 初始化空棋盘
    gameState.board = [];
    for (var y = 0; y < BOARD_SIZE; y++) {
        var row = [];
        for (var x = 0; x < BOARD_SIZE; x++) {
            row.push(0);
        }
        gameState.board.push(row);
    }

    // 清除所有棋子
    var stones = boardElement.getElementsByClassName('stone');
    while (stones.length > 0) {
        stones[0].parentNode.removeChild(stones[0]);
    }

    // 重置游戏状态
    gameState.currentPlayer = 1; // 玩家先手
    gameState.gameOver = false;

    // 更新状态显示
    updateStatusDisplay();
}

/**
 * 处理单元格点击事件
 */
function handleCellClick() {
    // 如果游戏结束或不是玩家回合或已断开连接，则忽略点击
    if (gameState.gameOver || gameState.currentPlayer !== 1 || gameState.disconnected) {
        return;
    }

    var x = parseInt(this.getAttribute('data-x'));
    var y = parseInt(this.getAttribute('data-y'));

    // 检查单元格是否为空
    if (gameState.board[y][x] === 0) {
        // 放置玩家的棋子
        placeStone(x, y, 1);

        // 检查是否获胜
        if (checkWin(x, y, 1)) {
            gameState.gameOver = true;
            gameState.playerScore++;
            // 保存分数到本地存储
            saveScoresToLocalStorage();
            updateScoreDisplay();
            updateStatusDisplay("恭喜！您赢了！");
            return;
        }

        // 切换到电脑回合
        gameState.currentPlayer = 2;
        updateStatusDisplay("思考中...");

        // 电脑延迟下棋
        setTimeout(computerMove, 1000);
    }
}

/**
 * 电脑下棋
 */
function computerMove() {
    // 如果游戏已结束或已断开连接，则不执行
    if (gameState.gameOver || gameState.disconnected) {
        return;
    }

    var move;

    // 开局策略，如果棋盘上棋子很少，优先考虑中心位置
    var stoneCount = countStones();
    if (stoneCount <= 2) {
        // 如果是电脑先手或只有一个棋子，优先选择中心位置
        var centerX = Math.floor(BOARD_SIZE / 2);
        var centerY = Math.floor(BOARD_SIZE / 2);

        // 如果中心点可用，直接选择
        if (gameState.board[centerY][centerX] === 0) {
            move = {x: centerX, y: centerY};
        } else {
            // 如果中心点已被占用，选择中心附近的点
            var centerMoves = [
                {x: centerX-1, y: centerY}, {x: centerX+1, y: centerY},
                {x: centerX, y: centerY-1}, {x: centerX, y: centerY+1},
                {x: centerX-1, y: centerY-1}, {x: centerX+1, y: centerY+1},
                {x: centerX-1, y: centerY+1}, {x: centerX+1, y: centerY-1}
            ];

            // 随机打乱中心点列表，增加变化性
            shuffleArray(centerMoves);

            for (var i = 0; i < centerMoves.length; i++) {
                var cm = centerMoves[i];
                if (cm.x >= 0 && cm.x < BOARD_SIZE && cm.y >= 0 && cm.y < BOARD_SIZE &&
                    gameState.board[cm.y][cm.x] === 0) {
                    move = cm;
                    break;
                }
            }
        }
    }

    // 如果没有选择开局点，则根据难度选择策略
    if (!move) {
        // 1. 尝试找出能赢的位置 (所有难度都会尝试直接获胜)
        var winMove = findWinningMove(2);
        if (winMove) {
            move = winMove;
        } else {
            // 根据难度选择不同策略
            switch (gameState.difficulty) {
                case 1: // 简单 - 贪心算法策略
                    // 简单模式下，使用纯贪心策略，按固定优先级选择位置
                    // 1. 首先检查是否能直接获胜
                    var winMove = findWinningMove(2);
                    if (winMove) {
                        move = winMove;
                    }
                    // 2. 其次检查是否需要阻止玩家获胜
                    else {
                        var blockMove = findWinningMove(1);
                        if (blockMove) {
                            move = blockMove;
                        }
                        // 3. 尝试形成四子连珠
                        else {
                            var fourInRowMove = findNInRowMove(2, 4);
                            if (fourInRowMove) {
                                move = fourInRowMove;
                            }
                            // 4. 尝试阻止玩家形成四子连珠
                            else {
                                var blockFourMove = findNInRowMove(1, 4);
                                if (blockFourMove) {
                                    move = blockFourMove;
                                }
                                // 5. 尝试形成三子连珠
                                else {
                                    var threeInRowMove = findNInRowMove(2, 3);
                                    if (threeInRowMove) {
                                        move = threeInRowMove;
                                    }
                                    // 6. 尝试阻止玩家形成三子连珠
                                    else {
                                        var blockThreeMove = findNInRowMove(1, 3);
                                        if (blockThreeMove) {
                                            move = blockThreeMove;
                                        }
                                        // 7. 尝试形成两子连珠
                                        else {
                                            var twoInRowMove = findNInRowMove(2, 2);
                                            if (twoInRowMove) {
                                                move = twoInRowMove;
                                            }
                                            // 8. 在玩家棋子附近下棋
                                            else {
                                                var nearbyMove = findNearbyMove();
                                                if (nearbyMove) {
                                                    move = nearbyMove;
                                                }
                                                // 9. 随机下棋
                                                else {
                                                    move = findRandomMove();
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 简单模式下，有 10% 的几率随机下棋，给玩家一些机会
                    if (Math.random() < 0.01) {
                        move = findRandomMove();
                    }
                    break;

                case 2: // 中等 - 使用极小极大算法和Alpha-Beta剪枝
                    // 1. 首先检查是否需要阻止玩家获胜（这是必须的防守）
                    var blockMove = findWinningMove(1);
                    if (blockMove) {
                        move = blockMove;
                    } else {
                        // 2. 检查是否需要阻止玩家形成四子连珠（加强防守）
                        var blockFourMove = findNInRowMove(1, 4);
                        if (blockFourMove) {
                            move = blockFourMove;
                        } else {
                            // 3. 尝试寻找可能形成四子连珠的位置（进攻）
                            var fourInRowMove = findNInRowMove(2, 4);
                            if (fourInRowMove) {
                                move = fourInRowMove;
                            } else {
                                // 4. 检查是否需要阻止玩家形成活三（加强防守）
                                var blockThreatsMove = findPlayerThreats(1);
                                if (blockThreatsMove) {
                                    move = blockThreatsMove;
                                } else {
                                    // 5. 使用威胁空间搜索找出关键威胁点（进攻）
                                    var threatMove = findThreatMove(2);
                                    if (threatMove) {
                                        move = threatMove;
                                    } else {
                                        // 6. 使用极小极大算法和Alpha-Beta剪枝搜索最佳位置
                                        var bestMove = findMinimaxMove(2, 3); // 搜索深度为3

                                        if (bestMove) {
                                            // 中等难度下，有10%的几率选择次优解（降低了概率）
                                            if (Math.random() < 0.1) {
                                                // 获取次优解
                                                var subOptimalMoves = findBestMoves(2, 5); // 找出前5个最佳位置
                                                if (subOptimalMoves.length > 1) {
                                                    // 随机选择一个非最优的位置
                                                    var randomIndex = Math.floor(Math.random() * (subOptimalMoves.length - 1)) + 1;
                                                    move = subOptimalMoves[randomIndex];
                                                } else {
                                                    move = bestMove;
                                                }
                                            } else {
                                                move = bestMove;
                                            }
                                        } else {
                                            // 如果没有找到好的位置，就随机下棋
                                            move = findRandomMove();
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;

                case 3: // 困难
                    // 困难模式下，始终选择最佳位置

                    // 首先检查是否有直接获胜的机会
                    var winMove = findWinningMove(2);
                    if (winMove) {
                        move = winMove;
                    }
                    // 然后检查是否需要阻止玩家获胜
                    else {
                        var blockMove = findWinningMove(1);
                        if (blockMove) {
                            move = blockMove;
                        } else {
                            // 使用评估函数找出最佳位置
                            var bestMoves = findBestMoves(2, 5); // 找出前5个最佳位置

                            if (bestMoves.length > 0) {
                                // 困难模式下，总是选择最佳位置
                                move = bestMoves[0];
                            } else {
                                // 如果没有找到好的位置，就随机下棋
                                move = findRandomMove();
                            }
                        }
                    }
                    break;

                default:
                    // 默认情况，使用中等难度策略
                    var blockMove = findWinningMove(1);
                    if (blockMove) {
                        move = blockMove;
                    } else {
                        var bestMoves = findBestMoves(2, 3);
                        if (bestMoves.length > 0) {
                            move = bestMoves[0];
                        } else {
                            move = findRandomMove();
                        }
                    }
            }
        }
    }

    // 如果仍然没有找到位置（这种情况很少发生），则随机下棋
    if (!move) {
        move = findRandomMove();
    }

    // 执行下棋动作
    placeStone(move.x, move.y, 2);

    // 检查电脑是否获胜
    if (checkWin(gameState.lastMove.x, gameState.lastMove.y, 2)) {
        gameState.gameOver = true;
        gameState.computerScore++;
        // 保存分数到本地存储
        saveScoresToLocalStorage();
        updateScoreDisplay();
        updateStatusDisplay("电脑赢了！");
        return;
    }

    // 检查是否平局
    if (isBoardFull()) {
        gameState.gameOver = true;
        updateStatusDisplay("平局！");
        return;
    }

    // 切换回玩家回合
    gameState.currentPlayer = 1;
    updateStatusDisplay("您的回合");
}

/**
 * 寻找能赢的位置
 * @param {number} player 玩家编号
 * @return {object|null} 位置对象 {x, y} 或 null
 */
function findWinningMove(player) {
    // 遍历所有空位
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                // 临时放置棋子
                gameState.board[y][x] = player;

                // 检查是否获胜
                if (checkWin(x, y, player)) {
                    // 恢复空位
                    gameState.board[y][x] = 0;
                    return {x: x, y: y};
                }

                // 恢复空位
                gameState.board[y][x] = 0;
            }
        }
    }

    return null;
}

/**
 * 寻找可能形成N子连珠的位置
 * @param {number} player 玩家编号
 * @param {number} n 连珠数量
 * @return {object|null} 位置对象 {x, y} 或 null
 */
function findNInRowMove(player, n) {
    // 遍历所有空位
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                // 临时放置棋子
                gameState.board[y][x] = player;

                // 检查是否形成N子连珠
                if (checkNInRow(x, y, player, n)) {
                    // 恢复空位
                    gameState.board[y][x] = 0;
                    return {x: x, y: y};
                }

                // 恢复空位
                gameState.board[y][x] = 0;
            }
        }
    }

    return null;
}

/**
 * 检查是否形成N子连珠
 * @param {number} x X坐标
 * @param {number} y Y坐标
 * @param {number} player 玩家编号
 * @param {number} n 连珠数量
 * @return {boolean} 是否形成N子连珠
 */
function checkNInRow(x, y, player, n) {
    // 检查方向: 水平、垂直、左下到右上、左上到右下
    var directions = [
        {dx: 1, dy: 0},  // 水平
        {dx: 0, dy: 1},  // 垂直
        {dx: 1, dy: 1},  // 右下
        {dx: 1, dy: -1}  // 右上
    ];

    for (var i = 0; i < directions.length; i++) {
        var dir = directions[i];
        var count = 1;  // 从1开始，因为已经包含了当前位置

        // 向一个方向计数
        var nx = x + dir.dx;
        var ny = y + dir.dy;
        while (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE && gameState.board[ny][nx] === player) {
            count++;
            nx += dir.dx;
            ny += dir.dy;
        }

        // 向相反方向计数
        nx = x - dir.dx;
        ny = y - dir.dy;
        while (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE && gameState.board[ny][nx] === player) {
            count++;
            nx -= dir.dx;
            ny -= dir.dy;
        }

        // 如果连续N个或更多，则返回true
        if (count >= n) {
            return true;
        }
    }

    return false;
}

/**
 * 寻找最佳的三子连珠位置（双三或活三）
 * @return {object|null} 位置对象 {x, y} 或 null
 */
function findBestThreeInRowMove() {
    var bestMoves = [];
    var bestScore = 0;

    // 遍历所有空位
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                // 计算该位置的分数
                var score = evaluatePosition(x, y, 2);

                if (score > bestScore) {
                    bestScore = score;
                    bestMoves = [{x: x, y: y}];
                } else if (score === bestScore && score > 0) {
                    bestMoves.push({x: x, y: y});
                }
            }
        }
    }

    // 如果有最佳位置，随机选择一个
    if (bestMoves.length > 0) {
        var randomIndex = Math.floor(Math.random() * bestMoves.length);
        return bestMoves[randomIndex];
    }

    return null;
}

/**
 * 评估一个位置的分数
 * @param {number} x X坐标
 * @param {number} y Y坐标
 * @param {number} player 玩家编号
 * @return {number} 分数
 */
function evaluatePosition(x, y, player) {
    // 记录原始状态
    var originalValue = gameState.board[y][x];
    // 临时放置棋子
    gameState.board[y][x] = player;

    var score = 0;
    var directions = [
        {dx: 1, dy: 0},  // 水平
        {dx: 0, dy: 1},  // 垂直
        {dx: 1, dy: 1},  // 右下
        {dx: 1, dy: -1}  // 右上
    ];

    // 计算每个方向的连珠情况
    var patternCounts = {
        'five': 0,         // 五子连珠
        'openFour': 0,     // 活四
        'fourWithOneEnd': 0, // 半活四
        'openThree': 0,    // 活三
        'threeWithOneEnd': 0, // 半活三
        'openTwo': 0,      // 活二
        'twoWithOneEnd': 0  // 半活二
    };

    // 计算棋形分数
    for (var i = 0; i < directions.length; i++) {
        var dir = directions[i];
        var pattern = getLinePattern(x, y, dir.dx, dir.dy, player);

        // 根据棋形给予分数
        if (pattern.count >= 5) {
            patternCounts.five++;
            score += 100000; // 五子连珠，直接获胜
        } else if (pattern.count === 4) {
            if (pattern.openEnds === 2) {
                patternCounts.openFour++;
                score += 10000; // 活四，几乎必胜
            } else if (pattern.openEnds === 1) {
                patternCounts.fourWithOneEnd++;
                score += 1000; // 半活四，强势
            }
        } else if (pattern.count === 3) {
            if (pattern.openEnds === 2) {
                patternCounts.openThree++;
                score += 500; // 活三，很强
            } else if (pattern.openEnds === 1) {
                patternCounts.threeWithOneEnd++;
                score += 100; // 半活三
            }
        } else if (pattern.count === 2) {
            if (pattern.openEnds === 2) {
                patternCounts.openTwo++;
                score += 50; // 活二
            } else if (pattern.openEnds === 1) {
                patternCounts.twoWithOneEnd++;
                score += 10; // 半活二
            }
        }
    }

    // 棋形组合给予额外分数
    // 双四、四三等强力组合
    if (patternCounts.openFour >= 2) {
        score += 50000; // 双活四，必胜
    }
    if (patternCounts.openFour >= 1 && patternCounts.fourWithOneEnd >= 1) {
        score += 30000; // 活四 + 半活四，很强
    }
    if (patternCounts.openFour >= 1 && patternCounts.openThree >= 1) {
        score += 20000; // 活四 + 活三，很强
    }
    if (patternCounts.fourWithOneEnd >= 2) {
        score += 15000; // 双半活四，很强
    }
    if (patternCounts.openThree >= 2) {
        score += 5000; // 双活三，很强
    }
    if (patternCounts.openThree >= 1 && patternCounts.threeWithOneEnd >= 1) {
        score += 1000; // 活三 + 半活三
    }
    if (patternCounts.threeWithOneEnd >= 2) {
        score += 500; // 双半活三
    }
    if (patternCounts.openTwo >= 2) {
        score += 100; // 双活二
    }

    // 位置权重，优先考虑棋盘中心位置
    var centerX = Math.floor(BOARD_SIZE / 2);
    var centerY = Math.floor(BOARD_SIZE / 2);
    var distanceToCenter = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
    var maxDistance = Math.sqrt(Math.pow(centerX, 2) + Math.pow(centerY, 2));
    var positionScore = 30 * (1 - distanceToCenter / maxDistance);
    score += positionScore;

    // 棋子聚集度评估，靠近已有棋子的位置分数更高
    var proximityScore = evaluateProximity(x, y, player);
    score += proximityScore;

    // 恢复原始状态
    gameState.board[y][x] = originalValue;

    return score;
}

/**
 * 获取一条线上的棋形模式
 * @param {number} x X坐标
 * @param {number} y Y坐标
 * @param {number} dx X方向增量
 * @param {number} dy Y方向增量
 * @param {number} player 玩家编号
 * @return {object} 棋形模式信息
 */
function getLinePattern(x, y, dx, dy, player) {
    var count = 1;  // 从1开始，因为已经包含了当前位置
    var openEnds = 0; // 开放端数量

    // 向一个方向计数并检查是否开放
    var nx = x + dx;
    var ny = y + dy;
    var hasOpenEnd1 = false;

    while (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE) {
        if (gameState.board[ny][nx] === player) {
            count++;
            nx += dx;
            ny += dy;
        } else if (gameState.board[ny][nx] === 0) {
            hasOpenEnd1 = true;
            break;
        } else {
            break;
        }
    }

    // 向相反方向计数并检查是否开放
    nx = x - dx;
    ny = y - dy;
    var hasOpenEnd2 = false;

    while (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE) {
        if (gameState.board[ny][nx] === player) {
            count++;
            nx -= dx;
            ny -= dy;
        } else if (gameState.board[ny][nx] === 0) {
            hasOpenEnd2 = true;
            break;
        } else {
            break;
        }
    }

    // 计算开放端数量
    if (hasOpenEnd1) openEnds++;
    if (hasOpenEnd2) openEnds++;

    return {
        count: count,
        openEnds: openEnds
    };
}

/**
 * 评估位置与其他棋子的聚集度
 * @param {number} x X坐标
 * @param {number} y Y坐标
 * @param {number} player 玩家编号
 * @return {number} 聚集度分数
 */
function evaluateProximity(x, y, player) {
    var score = 0;
    var opponent = player === 1 ? 2 : 1;
    var playerStoneCount = 0;
    var opponentStoneCount = 0;

    // 检查周围3x3区域内的棋子
    for (var dy = -2; dy <= 2; dy++) {
        for (var dx = -2; dx <= 2; dx++) {
            if (dx === 0 && dy === 0) continue;

            var nx = x + dx;
            var ny = y + dy;

            if (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE) {
                if (gameState.board[ny][nx] === player) {
                    playerStoneCount++;
                    // 距离越近分数越高
                    var distance = Math.max(Math.abs(dx), Math.abs(dy));
                    score += (3 - distance) * 2;
                } else if (gameState.board[ny][nx] === opponent) {
                    opponentStoneCount++;
                    // 靠近对手棋子也有一定价值（阻挡价值）
                    var distance = Math.max(Math.abs(dx), Math.abs(dy));
                    score += (3 - distance) * 1;
                }
            }
        }
    }

    // 如果周围有多个自己的棋子，给予额外分数
    if (playerStoneCount >= 3) {
        score += 10;
    }

    return score;
}

/**
 * 寻找玩家棋子附近的位置
 * @return {object|null} 位置对象 {x, y} 或 null
 */
function findNearbyMove() {
    var candidates = [];

    // 遍历所有空位
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                // 检查周围8个方向是否有玩家的棋子
                var hasPlayerStoneNearby = false;

                for (var dy = -1; dy <= 1; dy++) {
                    for (var dx = -1; dx <= 1; dx++) {
                        if (dx === 0 && dy === 0) continue;

                        var nx = x + dx;
                        var ny = y + dy;

                        if (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE) {
                            if (gameState.board[ny][nx] === 1) {
                                hasPlayerStoneNearby = true;
                                break;
                            }
                        }
                    }
                    if (hasPlayerStoneNearby) break;
                }

                if (hasPlayerStoneNearby) {
                    candidates.push({x: x, y: y});
                }
            }
        }
    }

    // 如果有候选位置，随机选择一个
    if (candidates.length > 0) {
        var randomIndex = Math.floor(Math.random() * candidates.length);
        return candidates[randomIndex];
    }

    return null;
}

/**
 * 寻找最佳位置
 * @param {number} player 玩家编号
 * @param {number} limit 返回的最佳位置数量限制
 * @return {Array} 按分数降序排列的最佳位置数组
 */
function findBestMoves(player, limit) {
    var candidates = [];

    // 评估所有空位
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                var score = evaluatePosition(x, y, player);
                candidates.push({x: x, y: y, score: score});
            }
        }
    }

    // 按分数降序排序
    candidates.sort(function(a, b) {
        return b.score - a.score;
    });

    // 取前 N 个最佳位置
    var bestMoves = [];
    var count = Math.min(limit, candidates.length);
    for (var i = 0; i < count; i++) {
        bestMoves.push({x: candidates[i].x, y: candidates[i].y});
    }

    return bestMoves;
}

/**
 * 使用极小极大算法和Alpha-Beta剪枝寻找最佳位置
 * @param {number} player 玩家编号
 * @param {number} depth 搜索深度
 * @return {object|null} 最佳位置 {x, y} 或 null
 */
function findMinimaxMove(player, depth) {
    var bestScore = -Infinity;
    var bestMove = null;

    // 获取所有可能的移动，并按初步评估排序以提高剪枝效率
    var possibleMoves = [];
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                // 只考虑周围有棋子的位置，减少搜索空间
                if (hasAdjacentStone(x, y, 2)) {
                    var score = evaluatePosition(x, y, player);
                    possibleMoves.push({x: x, y: y, score: score});
                }
            }
        }
    }

    // 按初步评估分数排序，优先考虑高分位置
    possibleMoves.sort(function(a, b) {
        return b.score - a.score;
    });

    // 限制搜索的位置数量，提高效率
    var movesToSearch = possibleMoves.slice(0, Math.min(15, possibleMoves.length));

    // 如果没有可能的移动，返回null
    if (movesToSearch.length === 0) {
        return null;
    }

    // 对每个可能的移动进行极小极大搜索
    for (var i = 0; i < movesToSearch.length; i++) {
        var move = movesToSearch[i];

        // 模拟下棋
        gameState.board[move.y][move.x] = player;

        // 递归调用极小极大算法
        var score = minimax(move.x, move.y, depth - 1, -Infinity, Infinity, false, player);

        // 恢复棋盘
        gameState.board[move.y][move.x] = 0;

        // 更新最佳分数和最佳移动
        if (score > bestScore) {
            bestScore = score;
            bestMove = move;
        }
    }

    return bestMove;
}

/**
 * 极小极大算法实现，带Alpha-Beta剪枝
 * @param {number} x 最后一步的X坐标
 * @param {number} y 最后一步的Y坐标
 * @param {number} depth 当前搜索深度
 * @param {number} alpha Alpha值
 * @param {number} beta Beta值
 * @param {boolean} isMaximizing 是否是最大化玩家
 * @param {number} originalPlayer 原始玩家
 * @return {number} 评估分数
 */
function minimax(x, y, depth, alpha, beta, isMaximizing, originalPlayer) {
    var currentPlayer = isMaximizing ? originalPlayer : (originalPlayer === 1 ? 2 : 1);
    var opponent = currentPlayer === 1 ? 2 : 1;

    // 检查是否获胜
    if (checkWin(x, y, opponent)) {
        return isMaximizing ? -10000 : 10000;
    }

    // 达到搜索深度或棋盘已满，返回评估分数
    if (depth === 0 || isBoardFull()) {
        return evaluateBoard(originalPlayer);
    }

    // 获取可能的移动
    var possibleMoves = [];
    for (var ny = 0; ny < BOARD_SIZE; ny++) {
        for (var nx = 0; nx < BOARD_SIZE; nx++) {
            if (gameState.board[ny][nx] === 0) {
                // 只考虑周围有棋子的位置
                if (hasAdjacentStone(nx, ny, 2)) {
                    possibleMoves.push({x: nx, y: ny});
                }
            }
        }
    }

    // 如果没有可能的移动，返回评估分数
    if (possibleMoves.length === 0) {
        return evaluateBoard(originalPlayer);
    }

    if (isMaximizing) {
        var maxScore = -Infinity;

        for (var i = 0; i < possibleMoves.length; i++) {
            var move = possibleMoves[i];

            // 模拟下棋
            gameState.board[move.y][move.x] = currentPlayer;

            // 递归调用
            var score = minimax(move.x, move.y, depth - 1, alpha, beta, false, originalPlayer);

            // 恢复棋盘
            gameState.board[move.y][move.x] = 0;

            // 更新最大分数和Alpha值
            maxScore = Math.max(maxScore, score);
            alpha = Math.max(alpha, score);

            // Alpha-Beta剪枝
            if (beta <= alpha) {
                break;
            }
        }

        return maxScore;
    } else {
        var minScore = Infinity;

        for (var i = 0; i < possibleMoves.length; i++) {
            var move = possibleMoves[i];

            // 模拟下棋
            gameState.board[move.y][move.x] = currentPlayer;

            // 递归调用
            var score = minimax(move.x, move.y, depth - 1, alpha, beta, true, originalPlayer);

            // 恢复棋盘
            gameState.board[move.y][move.x] = 0;

            // 更新最小分数和Beta值
            minScore = Math.min(minScore, score);
            beta = Math.min(beta, score);

            // Alpha-Beta剪枝
            if (beta <= alpha) {
                break;
            }
        }

        return minScore;
    }
}

/**
 * 评估整个棋盘的分数
 * @param {number} player 玩家编号
 * @return {number} 评估分数
 */
function evaluateBoard(player) {
    var opponent = player === 1 ? 2 : 1;
    var score = 0;

    // 遍历所有空位，评估每个位置的分数
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                // 评估如果玩家在此位置下棋的分数
                var playerScore = evaluatePosition(x, y, player);

                // 评估如果对手在此位置下棋的分数
                var opponentScore = evaluatePosition(x, y, opponent);

                // 综合考虑进攻和防守
                score += playerScore - opponentScore * 0.8;
            }
        }
    }

    return score;
}

/**
 * 检查位置周围是否有棋子
 * @param {number} x X坐标
 * @param {number} y Y坐标
 * @param {number} distance 检查的距离
 * @return {boolean} 是否有棋子
 */
function hasAdjacentStone(x, y, distance) {
    for (var dy = -distance; dy <= distance; dy++) {
        for (var dx = -distance; dx <= distance; dx++) {
            if (dx === 0 && dy === 0) continue;

            var nx = x + dx;
            var ny = y + dy;

            if (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE) {
                if (gameState.board[ny][nx] !== 0) {
                    return true;
                }
            }
        }
    }

    return false;
}

/**
 * 寻找玩家的威胁并阻止
 * @param {number} player 玩家编号（通常是1，表示人类玩家）
 * @return {object|null} 阻止威胁的最佳位置 {x, y} 或 null
 */
function findPlayerThreats(player) {
    var bestMove = null;
    var bestScore = -Infinity;

    // 检查玩家的连续棋子并阻止威胁
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                var blockScore = 0;

                // 检查这个位置是否能阻止玩家的威胁
                // 先检查玩家在这里下棋会形成什么棋形
                gameState.board[y][x] = player;
                var playerPatterns = countPatterns(x, y, player);
                gameState.board[y][x] = 0;

                // 检查水平、垂直和对角线上的连续棋子
                var directions = [
                    {dx: 1, dy: 0},  // 水平
                    {dx: 0, dy: 1},  // 垂直
                    {dx: 1, dy: 1},  // 右下
                    {dx: 1, dy: -1}  // 右上
                ];

                for (var i = 0; i < directions.length; i++) {
                    var dir = directions[i];
                    var consecutiveCount = 0;
                    var openEnds = 0;

                    // 向一个方向检查
                    var nx = x + dir.dx;
                    var ny = y + dir.dy;
                    while (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE) {
                        if (gameState.board[ny][nx] === player) {
                            consecutiveCount++;
                            nx += dir.dx;
                            ny += dir.dy;
                        } else if (gameState.board[ny][nx] === 0) {
                            openEnds++;
                            break;
                        } else {
                            break;
                        }
                    }

                    // 向相反方向检查
                    nx = x - dir.dx;
                    ny = y - dir.dy;
                    while (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE) {
                        if (gameState.board[ny][nx] === player) {
                            consecutiveCount++;
                            nx -= dir.dx;
                            ny -= dir.dy;
                        } else if (gameState.board[ny][nx] === 0) {
                            openEnds++;
                            break;
                        } else {
                            break;
                        }
                    }

                    // 根据连续棋子数量和开放端计算阻止分数
                    if (consecutiveCount >= 3) {
                        if (openEnds >= 1) {
                            // 如果有至少3个连续棋子且至少一端开放，这是高优先级的阻止点
                            blockScore = Math.max(blockScore, 3000 + consecutiveCount * 500 + openEnds * 300);
                        } else {
                            blockScore = Math.max(blockScore, 1000 + consecutiveCount * 300);
                        }
                    } else if (consecutiveCount === 2) {
                        if (openEnds === 2) {
                            // 两个连续棋子且两端开放，也是需要阻止的
                            blockScore = Math.max(blockScore, 800);
                        }
                    }
                }

                // 考虑玩家的棋形威胁
                if (playerPatterns.openThree >= 2) {
                    // 双活三，非常危险
                    blockScore = Math.max(blockScore, 5000);
                } else if (playerPatterns.openThree >= 1) {
                    // 单活三，很危险
                    blockScore = Math.max(blockScore, 2500);
                } else if (playerPatterns.threeWithOneEnd >= 2) {
                    // 双半活三，也很危险
                    blockScore = Math.max(blockScore, 1500);
                } else if (playerPatterns.threeWithOneEnd >= 1) {
                    // 单半活三
                    blockScore = Math.max(blockScore, 800);
                }

                // 更新最佳阻止点
                if (blockScore > bestScore) {
                    bestScore = blockScore;
                    bestMove = {x: x, y: y};
                }
            }
        }
    }

    // 只有当阻止分数足够高时才返回阻止点
    return bestScore >= 800 ? bestMove : null;
}

/**
 * 寻找威胁空间中的关键点
 * @param {number} player 玩家编号
 * @return {object|null} 威胁点 {x, y} 或 null
 */
function findThreatMove(player) {
    var opponent = player === 1 ? 2 : 1;
    var bestMove = null;
    var bestScore = -Infinity;

    // 寻找双三、活三等威胁点
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                // 检查是否能形成双三或活三
                gameState.board[y][x] = player;

                var threatScore = 0;
                var patternCounts = countPatterns(x, y, player);

                // 计算威胁分数
                if (patternCounts.openThree >= 2) {
                    // 双活三，强力威胁
                    threatScore = 5000;
                } else if (patternCounts.openThree >= 1 && patternCounts.threeWithOneEnd >= 1) {
                    // 活三+半活三，较强威胁
                    threatScore = 2000;
                } else if (patternCounts.openThree >= 1) {
                    // 单活三，一般威胁
                    threatScore = 1000;
                } else if (patternCounts.threeWithOneEnd >= 2) {
                    // 双半活三，一般威胁
                    threatScore = 800;
                }

                // 检查对手是否能在此位置形成威胁
                gameState.board[y][x] = opponent;
                var opponentPatternCounts = countPatterns(x, y, opponent);

                // 如果对手能形成双三或活三，这个位置也很重要
                if (opponentPatternCounts.openThree >= 2) {
                    threatScore = Math.max(threatScore, 4500);
                } else if (opponentPatternCounts.openThree >= 1) {
                    threatScore = Math.max(threatScore, 900);
                }

                // 恢复空位
                gameState.board[y][x] = 0;

                // 更新最佳威胁点
                if (threatScore > bestScore) {
                    bestScore = threatScore;
                    bestMove = {x: x, y: y};
                }
            }
        }
    }

    // 只有当威胁分数足够高时才返回威胁点
    return bestScore >= 800 ? bestMove : null;
}

/**
 * 计算各种棋形的数量
 * @param {number} x X坐标
 * @param {number} y Y坐标
 * @param {number} player 玩家编号
 * @return {object} 各种棋形的数量
 */
function countPatterns(x, y, player) {
    var directions = [
        {dx: 1, dy: 0},  // 水平
        {dx: 0, dy: 1},  // 垂直
        {dx: 1, dy: 1},  // 右下
        {dx: 1, dy: -1}  // 右上
    ];

    var patternCounts = {
        'five': 0,         // 五子连珠
        'openFour': 0,     // 活四
        'fourWithOneEnd': 0, // 半活四
        'openThree': 0,    // 活三
        'threeWithOneEnd': 0, // 半活三
        'openTwo': 0,      // 活二
        'twoWithOneEnd': 0  // 半活二
    };

    // 计算棋形
    for (var i = 0; i < directions.length; i++) {
        var dir = directions[i];
        var pattern = getLinePattern(x, y, dir.dx, dir.dy, player);

        // 根据棋形更新计数
        if (pattern.count >= 5) {
            patternCounts.five++;
        } else if (pattern.count === 4) {
            if (pattern.openEnds === 2) {
                patternCounts.openFour++;
            } else if (pattern.openEnds === 1) {
                patternCounts.fourWithOneEnd++;
            }
        } else if (pattern.count === 3) {
            if (pattern.openEnds === 2) {
                patternCounts.openThree++;
            } else if (pattern.openEnds === 1) {
                patternCounts.threeWithOneEnd++;
            }
        } else if (pattern.count === 2) {
            if (pattern.openEnds === 2) {
                patternCounts.openTwo++;
            } else if (pattern.openEnds === 1) {
                patternCounts.twoWithOneEnd++;
            }
        }
    }

    return patternCounts;
}

/**
 * 随机打乱数组
 * @param {Array} array 要打乱的数组
 */
function shuffleArray(array) {
    for (var i = array.length - 1; i > 0; i--) {
        var j = Math.floor(Math.random() * (i + 1));
        var temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }
}

/**
 * 计算棋盘上的棋子数量
 * @return {number} 棋子数量
 */
function countStones() {
    var count = 0;
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] !== 0) {
                count++;
            }
        }
    }
    return count;
}

/**
 * 寻找随机空位
 * @return {object} 位置对象 {x, y}
 */
function findRandomMove() {
    var emptyPositions = [];

    // 收集所有空位
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                emptyPositions.push({x: x, y: y});
            }
        }
    }

    // 随机选择一个空位
    var randomIndex = Math.floor(Math.random() * emptyPositions.length);
    return emptyPositions[randomIndex];
}

/**
 * 检查棋盘是否已满
 * @return {boolean} 是否已满
 */
function isBoardFull() {
    for (var y = 0; y < BOARD_SIZE; y++) {
        for (var x = 0; x < BOARD_SIZE; x++) {
            if (gameState.board[y][x] === 0) {
                return false;
            }
        }
    }
    return true;
}

/**
 * 放置棋子
 * @param {number} x X坐标
 * @param {number} y Y坐标
 * @param {number} player 玩家编号 (1=黑, 2=白)
 */
function placeStone(x, y, player) {
    // 更新棋盘状态
    gameState.board[y][x] = player;
    gameState.lastMove = {x: x, y: y};

    // 创建棋子元素
    var stone = document.createElement('div');
    stone.className = 'stone ' + (player === 1 ? 'black' : 'white') + ' stone-animation';
    stone.style.width = STONE_SIZE + 'px';
    stone.style.height = STONE_SIZE + 'px';
    stone.style.left = (x * CELL_SIZE + (CELL_SIZE - STONE_SIZE) / 2) + 'px';
    stone.style.top = (y * CELL_SIZE + (CELL_SIZE - STONE_SIZE) / 2) + 'px';

    // 添加到棋盘
    boardElement.appendChild(stone);

    // 在动画结束后移除动画类
    setTimeout(function() {
        stone.classList.remove('stone-animation');
    }, 500); // 500毫秒后移除动画类
}

/**
 * 检查是否获胜
 * @param {number} x 最后一步的X坐标
 * @param {number} y 最后一步的Y坐标
 * @param {number} player 玩家编号
 * @return {boolean} 是否获胜
 */
function checkWin(x, y, player) {
    // 检查方向: 水平、垂直、左下到右上、左上到右下
    var directions = [
        {dx: 1, dy: 0},  // 水平
        {dx: 0, dy: 1},  // 垂直
        {dx: 1, dy: 1},  // 右下
        {dx: 1, dy: -1}  // 右上
    ];

    for (var i = 0; i < directions.length; i++) {
        var dir = directions[i];
        var count = 1;  // 从1开始，因为已经包含了当前位置

        // 向一个方向计数
        var nx = x + dir.dx;
        var ny = y + dir.dy;
        while (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE && gameState.board[ny][nx] === player) {
            count++;
            nx += dir.dx;
            ny += dir.dy;
        }

        // 向相反方向计数
        nx = x - dir.dx;
        ny = y - dir.dy;
        while (nx >= 0 && nx < BOARD_SIZE && ny >= 0 && ny < BOARD_SIZE && gameState.board[ny][nx] === player) {
            count++;
            nx -= dir.dx;
            ny -= dir.dy;
        }

        // 如果连续5个或更多，则获胜
        if (count >= 5) {
            return true;
        }
    }

    return false;
}

/**
 * 更新状态显示
 * @param {string} [message] 可选的状态消息
 */
function updateStatusDisplay(message) {
    if (message) {
        statusElement.textContent = "游戏状态: " + message;
    } else if (gameState.gameOver) {
        statusElement.textContent = "游戏状态: 游戏结束";
    } else if (gameState.currentPlayer === 1) {
        statusElement.textContent = "游戏状态: 您的回合 (黑子)";
    } else {
        statusElement.textContent = "游戏状态: 电脑回合 (白子)";
    }
}

/**
 * 更新分数显示
 */
function updateScoreDisplay() {
    playerScoreElement.textContent = gameState.playerScore;
    computerScoreElement.textContent = gameState.computerScore;
}

/**
 * 重新开始游戏
 */
function restartGame() {
    resetBoard();
}

/**
 * 开始心跳检测
 */
function startHeartbeat() {
    // 清除可能存在的旧心跳
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
    }

    // 设置心跳间隔 (5秒)
    heartbeatInterval = setInterval(sendHeartbeat, 15000);

    // 立即发送一次心跳
    sendHeartbeat();
}

/**
 * 发送心跳请求
 */
function sendHeartbeat() {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', '/heartbeat', true);

    // 设置超时
    xhr.timeout = 5000;

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                // 心跳成功
                failedHeartbeats = 0;
                if (gameState.disconnected) {
                    // 恢复连接
                    gameState.disconnected = false;
                    updateConnectionStatus(true);
                }
            } else {
                // 心跳失败
                handleFailedHeartbeat();
            }
        }
    };

    xhr.ontimeout = function() {
        // 超时也算失败
        handleFailedHeartbeat();
    };

    xhr.onerror = function() {
        // 错误也算失败
        handleFailedHeartbeat();
    };

    try {
        xhr.send();
    } catch (e) {
        // 发送异常也算失败
        handleFailedHeartbeat();
    }
}

/**
 * 处理心跳失败
 */
function handleFailedHeartbeat() {
    failedHeartbeats++;

    if (failedHeartbeats >= MAX_FAILED_HEARTBEATS && !gameState.disconnected) {
        // 连续失败达到阈值，标记为断开连接
        gameState.disconnected = true;
        updateConnectionStatus(false);
    }
}

/**
 * 更新连接状态显示
 * @param {boolean} connected 是否已连接
 */
function updateConnectionStatus(connected) {
    if (connected) {
        connectionStatusElement.textContent = "连接状态: 已连接";
        connectionStatusElement.className = "connected";
    } else {
        connectionStatusElement.textContent = "连接状态: 已断开，无法继续游戏";
        connectionStatusElement.className = "disconnected";

        // 禁用棋盘点击
        var cells = document.getElementsByClassName('cell');
        for (var i = 0; i < cells.length; i++) {
            cells[i].style.pointerEvents = "none";
        }

        // 更新状态显示
        statusElement.textContent = "游戏状态: 连接已断开";
    }
}

/**
 * 获取游戏配置（仅获取难度设置）
 */
function fetchGameConfig() {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', '/api/config', true);

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var config = JSON.parse(xhr.responseText);
                    gameState.difficulty = config.difficulty || 1;

                    console.log('加载游戏配置，难度: ' + gameState.difficulty);
                } catch (e) {
                    console.error('解析游戏配置失败:', e);
                }
            }
        }
    };

    xhr.send();
}



// 页面加载完成后初始化游戏
window.onload = initGame;
