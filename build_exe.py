"""
打包脚本 - 将游戏服务器启动器打包为Windows可执行文件
使用PyInstaller进行打包，生成单个exe文件
"""
import os
import sys
import shutil
import subprocess

def check_pyinstaller():
    """检查是否安装了PyInstaller"""
    try:
        import PyInstaller
        print("PyInstaller已安装")
        return True
    except ImportError:
        print("PyInstaller未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("PyInstaller安装成功")
            return True
        except Exception as e:
            print(f"安装PyInstaller失败: {e}")
            return False

def build_exe():
    """构建可执行文件"""
    print("开始构建可执行文件...")

    # 清理之前的构建文件
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists("dist"):
        shutil.rmtree("dist")

    # 构建命令
    cmd = [
        "pyinstaller",
        "--name=游戏服务器",
        "--onefile",  # 生成单个exe文件
        "--windowed",  # 不显示控制台窗口
        "--icon=favicon.ico",  # 设置图标
        "--add-data=favicon.ico;.",  # 添加图标文件
        # 添加游戏资源文件
        "--add-data=games;games",  # 添加游戏目录
        "--add-data=utils;utils",  # 添加工具目录
       
        # 排除不需要的模块，减小文件大小
        "--exclude-module=numpy",
        "--exclude-module=pandas",
        "--exclude-module=matplotlib",
        "--exclude-module=scipy",
        "--exclude-module=PIL",
        "server_launcher.py"  # 主脚本
    ]

    # 执行构建
    try:
        subprocess.check_call(cmd)
        print("构建成功!")

        # 创建发布包
        create_release_package()
    except Exception as e:
        print(f"构建失败: {e}")

def create_release_package():
    """创建发布包，包含可执行文件和游戏文件夹"""
    print("正在创建发布包...")

    # 创建发布目录
    release_dir = "release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)

    # 复制可执行文件
    shutil.copy("dist/游戏服务器.exe", release_dir)

    # 复制游戏文件夹
    games_dir = os.path.join(release_dir, "games")
    os.makedirs(games_dir)

    # 复制所有游戏
    for game in os.listdir("games"):
        game_path = os.path.join("games", game)
        if os.path.isdir(game_path):
            dest_path = os.path.join(games_dir, game)
            print(f"复制游戏: {game} 从 {game_path} 到 {dest_path}")
            # 使用shutil.copytree确保所有文件和子目录都被复制
            shutil.copytree(game_path, dest_path)

    # 复制utils目录
    if os.path.exists("utils"):
        utils_dest = os.path.join(release_dir, "utils")
        print(f"复制工具目录: utils 到 {utils_dest}")
        shutil.copytree("utils", utils_dest)

    print(f"发布包创建成功! 文件位于 {os.path.abspath(release_dir)} 目录")
    print("使用说明:")
    print("1. 将整个release文件夹复制到目标计算机")
    print("2. 运行游戏服务器.exe启动游戏服务器")
    print("3. 默认管理员密码: 12340")

if __name__ == "__main__":
    if check_pyinstaller():
        build_exe()
    else:
        print("请先安装PyInstaller: pip install pyinstaller")
