"""
网络工具函数模块，提供获取本机IP地址和查找可用端口的功能
"""
import socket
import logging

def get_local_ip():
    """
    尝试获取本机在局域网中的IP地址
    
    返回:
        str: 成功时返回IP地址，失败时返回None
    """
    try:
        # 创建一个UDP套接字
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # 连接到一个外部地址（不需要真实连接）
        s.connect(("*******", 80))
        # 获取分配的IP
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception as e:
        logging.error(f"获取本机IP地址失败: {e}")
        return None

def find_available_port(start_port=8006):
    """
    从指定端口开始查找可用端口
    
    参数:
        start_port (int): 起始端口号
        
    返回:
        int: 可用的端口号
    """
    port = start_port
    while True:
        try:
            # 尝试绑定端口
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.bind(('', port))
            s.close()
            return port
        except OSError:
            # 端口被占用，尝试下一个
            port += 1
        except Exception as e:
            logging.error(f"查找可用端口时出错: {e}")
            # 出错时返回一个默认端口，实际使用时可能需要更好的错误处理
            return start_port + 1000
