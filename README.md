# 游戏服务器启动器

这是一个基于Tkinter的游戏服务器启动器，用于管理和启动HTML5游戏服务。

## 功能特点

- 简单易用的图形界面
- 支持多种游戏
- 自动查找可用端口
- 显示游戏统计信息
- 支持不同难度级别设置

## 打包说明

### 使用打包脚本

项目提供了一个打包脚本 `build_exe.py`，可以将应用程序打包为Windows可执行文件。

1. 确保已安装Python和必要的依赖
2. 运行打包脚本：

```bash
python build_exe.py
```

3. 打包完成后，在 `release` 目录中可以找到打包好的文件

### 手动打包

如果需要手动打包，可以使用PyInstaller：

```bash
pip install pyinstaller
pyinstaller --name="游戏服务器" --onefile --windowed --icon=favicon.ico --add-data="favicon.ico;." server_launcher.py
```

## 使用说明

### 开发环境

1. 确保已安装Python 3.6或更高版本
2. 安装必要的依赖：`tkinter`（通常随Python一起安装）
3. 运行 `server_launcher.py` 启动应用程序

### 打包后使用

1. 将打包后的 `游戏服务器.exe` 和 `games` 文件夹放在同一目录下
2. 双击 `游戏服务器.exe` 启动应用程序
3. 默认管理员密码：`12340`

## 游戏目录结构

每个游戏应放置在 `games` 目录下的单独文件夹中，例如：

```
games/
  ├── gomoku/
  │   ├── index.html
  │   ├── style.css
  │   └── script.js
  └── other_game/
      ├── index.html
      └── ...
```

## 注意事项

1. 打包后的程序会自动查找游戏目录，如果在当前目录下找不到 `games` 文件夹，会尝试在上级目录查找
2. 确保游戏文件夹结构正确，每个游戏必须有 `index.html` 作为入口文件
3. 默认管理员密码为 `12340`，可以在源代码中修改
